import { chromium } from 'playwright';

(async () => {
  // Inicia el navegador con configuración más realista
  const browser = await chromium.launch({
    headless: false, // Cambio a false para ver qué pasa
    slowMo: 1000 // Añade delay entre acciones
  });
  const page = await browser.newPage();

  // Configura user agent para parecer más humano
  await page.setExtraHTTPHeaders({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  });

  try {
    console.log('Navegando a TripAdvisor...');
    
    // Navega a la URL
    await page.goto("https://www.tripadvisor.com/Restaurant_Review-g1096406-d2092454-Reviews-High_House_Farm_Brewery-Matfen_Northumberland_England.html");

    console.log('Esperando a que cargue el contenido...');

    // Espera un poco para que la página cargue completamente
    await page.waitForTimeout(5000);

    // Intenta hacer scroll para cargar contenido dinámico
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2);
    });
    await page.waitForTimeout(2000);

    // Toma una captura de pantalla para ver qué está pasando
    await page.screenshot({ path: 'debug.png' });
    console.log('Captura de pantalla guardada como debug.png');

    // Busca diferentes selectores posibles
    const possibleSelectors = [
      'div[data-automation="reviewCard"]',
      '[data-test-target="review-card"]',
      '.review-container',
      '.reviewSelector',
      '[data-reviewid]',
      '.review',
      '[class*="review"]',
      '[id*="review"]',
      'div[class*="Review"]',
      '.listContainer div',
      '.ui_column div[class*="review"]'
    ];

    let reviewSelector = null;
    for (const selector of possibleSelectors) {
      const elements = await page.$$(selector);
      if (elements.length > 0) {
        console.log(`Encontrado selector: ${selector} con ${elements.length} elementos`);
        reviewSelector = selector;
        break;
      }
    }

    if (!reviewSelector) {
      console.log('No se encontraron reseñas con los selectores conocidos');
      console.log('Buscando elementos que contengan "review" en su HTML...');

      // Busca cualquier elemento que contenga texto relacionado con reseñas
      const reviewElements = await page.evaluate(() => {
        const allElements = document.querySelectorAll('*');
        const reviewRelated = [];

        for (let element of allElements) {
          const className = element.className || '';
          const id = element.id || '';
          const innerHTML = element.innerHTML || '';

          if (className.toLowerCase().includes('review') ||
              id.toLowerCase().includes('review') ||
              innerHTML.toLowerCase().includes('review')) {
            reviewRelated.push({
              tagName: element.tagName,
              className: className,
              id: id,
              textContent: element.textContent ? element.textContent.substring(0, 100) : ''
            });
          }
        }

        return reviewRelated.slice(0, 10); // Solo los primeros 10
      });

      console.log('Elementos relacionados con reviews encontrados:');
      console.log(JSON.stringify(reviewElements, null, 2));
      return;
    }

    console.log('Extrayendo reseñas...');
    
    // Extrae las reseñas usando el selector encontrado
    const reviews = await page.$$eval(reviewSelector, (cards) => {
      return cards.map((card, index) => {
        // Múltiples selectores posibles para cada campo
        const authorSelectors = [
          'div.QIHsu.Zb span.biGQs._P.fiohW.fOtGX a',
          '.memberOverlayLink',
          '[data-test-target="review-username"]',
          '.ui_header_link'
        ];

        const titleSelectors = [
          'div[data-test-target="review-title"] a',
          '.noQuotes',
          '.quote'
        ];

        const bodySelectors = [
          'div[data-test-target="review-body"] span.ThzWO span.JguWG',
          '.partial_entry',
          '.entry .p'
        ];

        const dateSelectors = [
          'div.fUmAk div.biGQs._P.pZUbB.ncFvv.osNWb',
          '.ratingDate',
          '.relativeDate'
        ];

        // Función helper para encontrar texto con múltiples selectores
        const findText = (selectors) => {
          for (const selector of selectors) {
            const element = card.querySelector(selector);
            if (element && element.textContent.trim()) {
              return element.textContent.trim();
            }
          }
          return "No encontrado";
        };

        const author = findText(authorSelectors);
        const title = findText(titleSelectors);
        const body = findText(bodySelectors);
        const date = findText(dateSelectors).replace("Written ", "");

        // Para ubicación, busca en varios lugares
        const location = card.querySelector('div.vYLts span.biGQs._P.pZUbB.osNWb span')?.textContent ||
                        card.querySelector('.userLoc')?.textContent ||
                        "No encontrado";

        return {
          index: index + 1,
          author,
          location,
          title,
          date,
          body
        };
      });
    });

    // Imprime las reseñas extraídas
    console.log(`Se encontraron ${reviews.length} reseñas:`);
    console.log(JSON.stringify(reviews, null, 2));

  } catch (error) {
    console.error('Error durante el scraping:', error);
  } finally {
    // Cierra el navegador
    await browser.close();
    console.log('Navegador cerrado.');
  }
})();
