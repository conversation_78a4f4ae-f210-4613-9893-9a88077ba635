import { chromium } from 'playwright';

(async () => {
  // Inicia el navegador con configuración más realista
  const browser = await chromium.launch({
    headless: false, // Cambio a false para ver qué pasa
    slowMo: 1000 // Añade delay entre acciones
  });
  const page = await browser.newPage();

  // Configura user agent para parecer más humano
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

  try {
    console.log('Navegando a TripAdvisor...');
    
    // Navega a la URL
    await page.goto("https://www.tripadvisor.com/Restaurant_Review-g1096406-d2092454-Reviews-High_House_Farm_Brewery-Matfen_Northumberland_England.html");

    console.log('Esperando a que cargue el contenido...');
    
    // Espera a que cargue el contenido principal
    await page.waitForSelector('div[data-automation="reviewCard"]', { timeout: 10000 });

    console.log('Extrayendo reseñas...');
    
    // Extrae las reseñas
    const reviews = await page.$$eval('div[data-automation="reviewCard"]', (cards) => {
      return cards.map(card => {
        const author = card.querySelector('div.QIHsu.Zb span.biGQs._P.fiohW.fOtGX a')?.textContent || "No encontrado";
        const location = card.querySelector('div.vYLts span.biGQs._P.pZUbB.osNWb span')?.textContent || "No encontrado";
        const title = card.querySelector('div[data-test-target="review-title"] a')?.textContent || "No encontrado";
        const date = card.querySelector('div.fUmAk div.biGQs._P.pZUbB.ncFvv.osNWb')?.textContent.replace("Written ", "") || "No encontrado";
        const body = card.querySelector('div[data-test-target="review-body"] span.ThzWO span.JguWG')?.textContent || "No encontrado";

        return { author, location, title, date, body };
      });
    });

    // Imprime las reseñas extraídas
    console.log(`Se encontraron ${reviews.length} reseñas:`);
    console.log(JSON.stringify(reviews, null, 2));

  } catch (error) {
    console.error('Error durante el scraping:', error);
  } finally {
    // Cierra el navegador
    await browser.close();
    console.log('Navegador cerrado.');
  }
})();
