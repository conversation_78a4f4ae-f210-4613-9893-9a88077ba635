import { chromium } from 'playwright';

(async () => {
  // Inicia el navegador
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();

  // Navega a la URL
  await page.goto("https://www.tripadvisor.com/Restaurant_Review-g1096406-d2092454-Reviews-High_House_Farm_Brewery-Matfen_Northumberland_England.html");

  // Espera a que cargue el contenido principal
  await page.waitForSelector('div[data-automation="reviewCard"]');

  // Extrae las reseñas
  const reviews = await page.$$eval('div[data-automation="reviewCard"]', (cards) => {
    return cards.map(card => {
      const author = card.querySelector('div.QIHsu.Zb span.biGQs._P.fiohW.fOtGX a')?.textContent || "No encontrado";
      const location = card.querySelector('div.vYLts span.biGQs._P.pZUbB.osNWb span')?.textContent || "No encontrado";
      const title = card.querySelector('div[data-test-target="review-title"] a')?.textContent || "No encontrado";
      const date = card.querySelector('div.fUmAk div.biGQs._P.pZUbB.ncFvv.osNWb')?.textContent.replace("Written ", "") || "No encontrado";
      const body = card.querySelector('div[data-test-target="review-body"] span.ThzWO span.JguWG')?.textContent || "No encontrado";

      return { author, location, title, date, body };
    });
  });

  // Imprime las reseñas extraídas
  console.log(reviews);

  // Cierra el navegador
  await browser.close();
})();